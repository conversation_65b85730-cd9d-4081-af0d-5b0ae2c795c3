import React, { useMemo, useState } from 'react';
import { useQueryClient } from 'react-query';
import { Trash2, TrendingUp, ExternalLink, RefreshCw, Activity, TrendingDown, GripVertical, Clock, CheckCircle, AlertCircle } from 'lucide-react';
import { Stock } from '@/types';
import { useBatchStockData, useBatchStockQuotes } from '@/hooks/useStockData';
import { detectVPattern } from '@/utils/patternDetection';
import { MiniFlowChart } from '@/components/DataDisplay/MiniFlowChart';
import { formatMoneyAuto } from '@/utils/formatters';
import { BatchRequestStatus } from '@/components/Common/BatchRequestStatus';
import { QUERY_KEYS } from '@/utils/queryClient';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface StockListProps {
  stocks: Stock[];
  onRemoveStock: (code: string) => void;
  onSelectStock?: (code: string) => void;
  selectedStock?: string | null;
  /** 是否显示实时监控数据 */
  showRealTimeData?: boolean;
  /** 是否全屏显示 */
  isFullScreen?: boolean;
  /** 拖拽排序回调 */
  onReorderStocks?: (activeId: string, overId: string) => void;
  /** 批量选择相关 */
  selectedStockCodes?: Set<string>;
  onSelectStockForBatch?: (code: string, selected: boolean) => void;
  onSelectAll?: (selectAll: boolean) => void;
  onBatchRemove?: () => void;
}

export function StockList({
  stocks,
  onRemoveStock,
  onSelectStock,
  selectedStock,
  showRealTimeData = false,
  isFullScreen = false,
  onReorderStocks,
  selectedStockCodes = new Set(),
  onSelectStockForBatch,
  onSelectAll,
  onBatchRemove
}: StockListProps) {
  const queryClient = useQueryClient();
  // 拖拽传感器配置
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 8px 移动距离后才开始拖拽
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 处理拖拽结束
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id && onReorderStocks) {
      onReorderStocks(active.id as string, over?.id as string);
    }
  };

  // 处理单个股票刷新
  const handleStockRefresh = async (stockCode: string) => {
    try {
      // 使单个股票的查询失效并重新获取
      await queryClient.invalidateQueries(QUERY_KEYS.STOCK_DATA(stockCode));

      // 同时刷新批量查询，确保数据一致性
      const stockCodes = stocks.map(s => s.code);
      await queryClient.invalidateQueries(QUERY_KEYS.STOCK_DATA_BATCH(stockCodes));

      console.log(`已刷新股票 ${stockCode} 的数据`);
    } catch (error) {
      console.error(`刷新股票 ${stockCode} 数据失败:`, error);
    }
  };

  // 获取股票代码列表
  const stockCodes = stocks.map(s => s.code);

  // 批量获取股票数据（仅在显示实时数据时）
  const batchDataQuery = useBatchStockData(stockCodes, 20, {
    // 使用统一配置的刷新间隔（3分钟，从60秒调整）
    refetchInterval: showRealTimeData ? undefined : undefined,
    enabled: showRealTimeData && stockCodes.length > 0
  });

  const {
    results,
    isLoading: dataLoading,
    isFetching,
    refetch
  } = batchDataQuery;

  // 批量获取股票行情数据（包含涨跌幅）
  const {
    data: quotesData
  } = useBatchStockQuotes(stockCodes, {
    // 使用统一配置的刷新间隔（3分钟，从60秒调整）
    refetchInterval: showRealTimeData ? undefined : undefined,
    enabled: showRealTimeData && stockCodes.length > 0
  });

  // 处理股票数据，集成V字型识别、实时数据和行情数据
  const stocksWithData = useMemo(() => {
    if (!showRealTimeData || !results || Object.keys(results).length === 0) {
      return stocks.map(stock => ({
        ...stock,
        data: null,
        hasVPattern: false,
        latestFlow: 0,
        change24h: 0,
        quote: null,
        changePercent: 0
      }));
    }

    return stocks.map(stock => {
      const data = results[stock.code];
      const quote = quotesData?.results?.[stock.code] || null;
      const hasVPattern = data?.klines ? detectVPattern(data.klines).hasVPattern : false;

      // 计算最新流入和变化
      const latestFlow = data?.klines?.[data.klines.length - 1]?.mainNetInflow || 0;
      const change24h = data?.klines && data.klines.length >= 2
        ? data.klines[data.klines.length - 1].mainNetInflow - data.klines[data.klines.length - 2].mainNetInflow
        : 0;

      return {
        ...stock,
        data,
        quote,
        hasVPattern,
        latestFlow,
        change24h,
        changePercent: quote?.changePercent || 0,
      };
    });
  }, [stocks, results, quotesData, showRealTimeData]);
  if (stocks.length === 0) {
    return (
      <div className={`text-center ${isFullScreen ? 'py-16' : 'py-8'}`}>
        <TrendingUp className={`${isFullScreen ? 'w-20 h-20' : 'w-12 h-12'} text-gray-300 mx-auto mb-3`} />
        <p className={`text-gray-500 mb-2 ${isFullScreen ? 'text-lg' : ''}`}>暂无股票代码</p>
        <p className={`${isFullScreen ? 'text-base' : 'text-sm'} text-gray-400`}>请添加股票代码开始监控</p>
      </div>
    );
  }

  return (
    <div className="space-y-2 h-full flex flex-col">
      {/* 列表头部 - 重新布局为单行 */}
      <div className={`flex items-center justify-between ${isFullScreen ? 'text-base' : 'text-sm'} text-gray-600 pb-2 border-b border-gray-200`}>
        <div className="flex items-center gap-3">
          {/* 全选复选框 */}
          {onSelectAll && (
            <div className="flex items-center gap-2">
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={selectedStockCodes.size === stocks.length && stocks.length > 0}
                  ref={(input) => {
                    if (input) {
                      input.indeterminate = selectedStockCodes.size > 0 && selectedStockCodes.size < stocks.length;
                    }
                  }}
                  onChange={(e) => onSelectAll(e.target.checked)}
                  className="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 focus:ring-2"
                />
                <span className="ml-1 text-xs text-gray-500">
                  {selectedStockCodes.size > 0 ? `已选 ${selectedStockCodes.size}` : '全选'}
                </span>
              </label>
            </div>
          )}

          <div className="flex items-center gap-2">
            <span>股票代码 ({stocks.length})</span>
            {showRealTimeData && (
              <>
                {isFetching && (
                  <RefreshCw className={`${isFullScreen ? 'w-4 h-4' : 'w-3 h-3'} animate-spin text-blue-500`} />
                )}
                <button
                  onClick={() => refetch()}
                  className={`${isFullScreen ? 'text-sm' : 'text-xs'} text-gray-500 hover:text-gray-700 transition-colors`}
                  disabled={isFetching}
                  title="刷新实时数据"
                >
                  <Activity className={`${isFullScreen ? 'w-4 h-4' : 'w-3 h-3'}`} />
                </button>
              </>
            )}
          </div>
        </div>

        {/* 中间区域：状态显示和更新时间 */}
        <div className="flex items-center gap-4 flex-1 justify-center">
          {/* 紧凑的状态显示 */}
          {showRealTimeData && stockCodes.length > 0 && (
            <div className="flex items-center gap-3">
              {/* 更新状态指示器 */}
              <div className="flex items-center gap-1">
                {dataLoading ? (
                  <Clock className="w-3 h-3 text-blue-500" />
                ) : results && Object.keys(results).length > 0 ? (
                  <CheckCircle className="w-3 h-3 text-green-500" />
                ) : (
                  <AlertCircle className="w-3 h-3 text-yellow-500" />
                )}
                <span className="text-xs text-gray-600">
                  {dataLoading ? '加载中' :
                   results ? `${Object.keys(results).length}/${stockCodes.length}` : '待更新'}
                </span>
              </div>

              {/* 更新时间显示 */}
              <div className="flex items-center gap-1 px-2 py-1 bg-gray-50 rounded text-xs text-gray-600">
                <Clock className="w-3 h-3" />
                <span>更新时间: {
                  (batchDataQuery as any).dataUpdatedAt
                    ? new Date((batchDataQuery as any).dataUpdatedAt).toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                      })
                    : '未更新'
                }</span>
              </div>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2">
          {/* 批量删除按钮 */}
          {onBatchRemove && selectedStockCodes.size > 0 && (
            <button
              onClick={onBatchRemove}
              className="flex items-center gap-1 px-2 py-1 text-xs bg-red-50 text-red-600 hover:bg-red-100 rounded transition-colors"
              title={`删除选中的 ${selectedStockCodes.size} 个股票`}
            >
              <Trash2 className="w-3 h-3" />
              删除选中 ({selectedStockCodes.size})
            </button>
          )}
          <span>操作</span>
        </div>
      </div>

      {/* 详细状态显示（仅在股票较多时显示） - 已隐藏 */}
      {false && showRealTimeData && stockCodes.length > 20 && (
        <BatchRequestStatus
          totalCount={stockCodes.length}
          successCount={results ? Object.keys(results).length : 0}
          failedCount={results ? stockCodes.length - Object.keys(results).length : 0}
          isLoading={dataLoading}
          isFetching={isFetching}
          onRetry={() => refetch()}
          showDetails={true}
          className="mb-3"
        />
      )}

      {/* 股票列表 - 支持拖拽排序，删除底部状态栏后占据全部可用空间 */}
      <div className="flex-1 overflow-y-auto">
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragEnd={handleDragEnd}
        >
          <SortableContext items={stockCodes} strategy={verticalListSortingStrategy}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
              {stocksWithData.map((stock) => (
                <SortableStockListItem
                  key={stock.code}
                  stock={stock}
                  isSelected={selectedStock === stock.code}
                  onSelect={onSelectStock}
                  onRemove={onRemoveStock}
                  showRealTimeData={showRealTimeData}
                  stockData={stock.data}
                  hasVPattern={stock.hasVPattern}
                  latestFlow={stock.latestFlow}
                  change24h={stock.change24h}
                  quote={stock.quote}
                  isFullScreen={isFullScreen}
                  isSelectedForBatch={selectedStockCodes.has(stock.code)}
                  onSelectForBatch={onSelectStockForBatch}
                  onRefresh={handleStockRefresh}
                />
              ))}
            </div>
          </SortableContext>
        </DndContext>
      </div>
    </div>
  );
}

interface StockListItemProps {
  stock: Stock;
  isSelected?: boolean;
  onSelect?: (code: string) => void;
  onRemove: (code: string) => void;
  showRealTimeData?: boolean;
  stockData?: any;
  hasVPattern?: boolean;
  latestFlow?: number;
  change24h?: number;
  quote?: any;
  isFullScreen?: boolean;
  /** 拖拽手柄属性 */
  dragHandleProps?: any;
  /** 是否正在拖拽 */
  isDragging?: boolean;
  /** 批量选择相关 */
  isSelectedForBatch?: boolean;
  onSelectForBatch?: (code: string, selected: boolean) => void;
  /** 单独刷新回调函数 */
  onRefresh?: (stockCode: string) => void;
}

// 可排序的股票列表项组件
function SortableStockListItem(props: StockListItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: props.stock.code });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div ref={setNodeRef} style={style}>
      <StockListItem
        {...props}
        dragHandleProps={{ ...attributes, ...listeners }}
        isDragging={isDragging}
      />
    </div>
  );
}

function StockListItem({
  stock,
  isSelected,
  onSelect,
  onRemove,
  showRealTimeData = false,
  stockData,
  hasVPattern = false,
  latestFlow = 0,
  change24h = 0,
  quote,
  isFullScreen = false,
  dragHandleProps,
  isDragging = false,
  isSelectedForBatch = false,
  onSelectForBatch,
  onRefresh
}: StockListItemProps) {
  const queryClient = useQueryClient();
  const [isRefreshing, setIsRefreshing] = useState(false);
  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm(`确定要删除股票 ${stock.code} 吗？`)) {
      onRemove(stock.code);
    }
  };

  const handleSelect = () => {
    if (onSelect) {
      onSelect(stock.code);
    }
  };

  const handleViewFlow = (e: React.MouseEvent) => {
    e.stopPropagation();
    const url = `https://data.eastmoney.com/zjlx/${stock.code}.html`;
    window.open(url, '_blank');
  };

  const handleBatchSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    e.stopPropagation();
    if (onSelectForBatch) {
      onSelectForBatch(stock.code, e.target.checked);
    }
  };

  // 处理单独刷新
  const handleRefresh = async (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发卡片点击

    if (isRefreshing) return;

    setIsRefreshing(true);

    try {
      // 使查询失效并重新获取数据
      await queryClient.invalidateQueries(QUERY_KEYS.STOCK_DATA(stock.code));

      // 如果有外部回调，也调用它
      if (onRefresh) {
        onRefresh(stock.code);
      }

      console.log(`已刷新股票 ${stock.code} 的数据`);
    } catch (error) {
      console.error(`刷新股票 ${stock.code} 数据失败:`, error);
    } finally {
      // 延迟一点时间再隐藏加载状态，确保用户能看到反馈
      setTimeout(() => {
        setIsRefreshing(false);
      }, 500);
    }
  };

  // 获取中国股市颜色（红涨绿跌）
  const getChineseStockColor = (value: number) => {
    if (value > 0) {
      return 'text-red-600'; // 红色 - 上涨/流入
    } else if (value < 0) {
      return 'text-green-600'; // 绿色 - 下跌/流出
    } else {
      return 'text-gray-600'; // 灰色 - 无变化
    }
  };

  return (
    <div
      className={`
        flex items-center ${isFullScreen ? 'p-3' : 'p-2'} rounded-lg border transition-all duration-200
        ${isSelected
          ? 'border-primary-500 bg-primary-50'
          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
        }
        ${onSelect ? 'cursor-pointer' : ''}
        ${isDragging ? 'shadow-lg scale-105' : ''}
        min-h-[48px]
      `}
      onClick={handleSelect}
    >
      {/* 批量选择复选框 */}
      {onSelectForBatch && (
        <div className="flex items-center justify-center w-6 h-6 mr-2">
          <input
            type="checkbox"
            checked={isSelectedForBatch}
            onChange={handleBatchSelect}
            onClick={(e) => e.stopPropagation()}
            className="w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 focus:ring-2 cursor-pointer"
            title="选择此股票"
          />
        </div>
      )}

      {/* 拖拽手柄 */}
      {dragHandleProps && (
        <div
          {...dragHandleProps}
          className="flex items-center justify-center w-6 h-6 mr-2 cursor-grab active:cursor-grabbing text-gray-400 hover:text-gray-600 transition-colors"
          onClick={(e) => e.stopPropagation()}
        >
          <GripVertical className="w-4 h-4" />
        </div>
      )}
      {/* 股票基本信息 */}
      <div className="flex items-center gap-2 min-w-0 flex-shrink-0">
        {/* 股票代码和名称 */}
        <div className="flex items-center gap-2">
          <span className={`${isFullScreen ? 'text-sm' : 'text-xs'} font-mono text-gray-600`}>
            {stock.code}
          </span>
          <span className={`${isFullScreen ? 'text-sm' : 'text-xs'} font-medium ${
            isSelected ? 'text-primary-700' : 'text-gray-900'
          } truncate`}>
            {stock.name}
          </span>
        </div>

        {/* 选中指示器 */}
        {isSelected && (
          <div className="w-2 h-2 bg-primary-500 rounded-full flex-shrink-0"></div>
        )}
      </div>

      {/* 实时监控数据 - 单行显示 */}
      {showRealTimeData && (
        <div className="flex items-center gap-3 flex-1 min-w-0 ml-3">
          {stockData && stockData.klines ? (
            <>
              {/* 主力净流入 */}
              <span
                className={`font-bold ${isFullScreen ? 'text-sm' : 'text-xs'} ${getChineseStockColor(latestFlow)} flex-shrink-0`}
              >
                {formatMoneyAuto(latestFlow)}
              </span>

              {/* 涨跌幅 */}
              {quote && quote.changePercent !== 0 && (
                <div className="flex items-center gap-1 flex-shrink-0">
                  {quote.changePercent > 0 ? (
                    <TrendingUp className="w-3 h-3 text-red-500" />
                  ) : (
                    <TrendingDown className="w-3 h-3 text-green-500" />
                  )}
                  <span
                    className={`${isFullScreen ? 'text-xs' : 'text-xs'} font-medium ${getChineseStockColor(quote.changePercent)}`}
                  >
                    {quote.changePercent > 0 ? '+' : ''}{quote.changePercent.toFixed(2)}%
                  </span>
                </div>
              )}

              {/* 24小时变化 */}
              {change24h !== 0 && (
                <div className="flex items-center gap-1 flex-shrink-0">
                  {change24h > 0 ? (
                    <TrendingUp className="w-3 h-3 text-red-500" />
                  ) : (
                    <TrendingDown className="w-3 h-3 text-green-500" />
                  )}
                  <span
                    className={`${isFullScreen ? 'text-xs' : 'text-xs'} font-medium ${getChineseStockColor(change24h)}`}
                  >
                    {change24h > 0 ? '+' : ''}{formatMoneyAuto(change24h)}
                  </span>
                </div>
              )}

              {/* V字形模式指示器 */}
              {hasVPattern && (
                <div className="flex items-center gap-1 flex-shrink-0">
                  <Activity className="w-3 h-3 text-red-500" />
                  <span className="text-xs text-red-600 font-medium">V型</span>
                </div>
              )}

              {/* 迷你图表 */}
              <div className="w-16 h-6 flex-shrink-0 ml-auto">
                <MiniFlowChart
                  klines={stockData.klines}
                  height={24}
                  showVPattern={true}
                />
              </div>
            </>
          ) : (
            /* 无数据状态 */
            <div className="flex items-center text-gray-400 flex-1">
              <span className="text-xs">加载中...</span>
            </div>
          )}
        </div>
      )}

      {/* 操作按钮组 */}
      <div className="flex items-center gap-1 ml-2 flex-shrink-0">
        {/* 刷新按钮 - 仅在显示实时数据时显示 */}
        {showRealTimeData && (
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className={`p-1 transition-colors duration-200 ${
              isRefreshing
                ? 'text-blue-400 cursor-not-allowed'
                : 'text-gray-400 hover:text-blue-600'
            }`}
            title="刷新此股票数据"
          >
            <RefreshCw className={`w-3 h-3 ${isRefreshing ? 'animate-spin' : ''}`} />
          </button>
        )}

        {/* 查看资金流向按钮 */}
        <button
          onClick={handleViewFlow}
          className="p-1 text-gray-400 hover:text-blue-600 transition-colors duration-200"
          title="查看资金流向"
        >
          <ExternalLink className="w-3 h-3" />
        </button>

        {/* 删除按钮 */}
        <button
          onClick={handleRemove}
          className="p-1 text-gray-400 hover:text-danger-600 transition-colors duration-200"
          title="删除股票"
        >
          <Trash2 className="w-3 h-3" />
        </button>
      </div>
    </div>
  );
}
