import React, { useState } from 'react';
import { useQueryClient } from 'react-query';
import { Stock } from '@/types';
import { ProcessedStockData } from '@/types/stock';
import { MiniFlowChart } from '@/components/DataDisplay/MiniFlowChart';
import { formatMoneyAuto, getFlowColor } from '@/utils/formatters';
import { TrendingUp, TrendingDown, Activity, RefreshCw } from 'lucide-react';
import { QUERY_KEYS } from '@/utils/queryClient';

interface StockMonitorItemProps {
  /** 股票信息 */
  stock: Stock;
  /** 股票数据 */
  data: ProcessedStockData | null;
  /** 是否有V字型模式 */
  hasVPattern?: boolean;
  /** 点击处理函数 */
  onClick?: (stockCode: string) => void;
  /** 是否为紧凑模式 */
  compact?: boolean;
  /** 单独刷新回调函数 */
  onRefresh?: (stockCode: string) => void;
}

/**
 * 单个股票监控项组件
 */
export const StockMonitorItem: React.FC<StockMonitorItemProps> = ({
  stock,
  data,
  hasVPattern = false,
  onClick,
  compact = false,
  onRefresh,
}) => {
  const queryClient = useQueryClient();
  const [isRefreshing, setIsRefreshing] = useState(false);

  // 获取最新的资金流入数据
  const latestFlow = data?.klines?.[data.klines.length - 1]?.mainNetInflow || 0;

  // 计算24小时变化（如果有足够数据）
  const change24h = data?.klines && data.klines.length >= 2
    ? data.klines[data.klines.length - 1].mainNetInflow - data.klines[data.klines.length - 2].mainNetInflow
    : 0;

  // 处理点击事件
  const handleClick = () => {
    if (onClick) {
      onClick(stock.code);
    }
  };

  // 处理单独刷新
  const handleRefresh = async (e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发卡片点击

    if (isRefreshing) return;

    setIsRefreshing(true);

    try {
      // 使查询失效并重新获取数据
      await queryClient.invalidateQueries(QUERY_KEYS.STOCK_DATA(stock.code));

      // 如果有外部回调，也调用它
      if (onRefresh) {
        onRefresh(stock.code);
      }

      console.log(`已刷新股票 ${stock.code} 的数据`);
    } catch (error) {
      console.error(`刷新股票 ${stock.code} 数据失败:`, error);
    } finally {
      // 延迟一点时间再隐藏加载状态，确保用户能看到反馈
      setTimeout(() => {
        setIsRefreshing(false);
      }, 500);
    }
  };

  // 如果没有数据，显示占位符
  if (!data || !data.klines || data.klines.length === 0) {
    return (
      <div
        className={`bg-white border rounded-lg p-3 ${compact ? 'p-2' : 'p-3'} ${
          onClick ? 'cursor-pointer hover:bg-gray-50' : ''
        }`}
        onClick={handleClick}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <span className={`font-medium text-gray-900 ${compact ? 'text-sm' : 'text-base'}`}>
                {stock.name}
              </span>
              <span className={`text-gray-500 ${compact ? 'text-xs' : 'text-sm'}`}>
                {stock.code}
              </span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-gray-400 text-sm">暂无数据</span>
            {/* 刷新按钮 */}
            <button
              onClick={handleRefresh}
              disabled={isRefreshing}
              className={`p-1 rounded-full transition-colors ${
                isRefreshing
                  ? 'text-blue-400 cursor-not-allowed'
                  : 'text-gray-400 hover:text-blue-500 hover:bg-blue-50'
              }`}
              title="刷新此股票数据"
            >
              <RefreshCw className={`w-3 h-3 ${isRefreshing ? 'animate-spin' : ''}`} />
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`bg-white border rounded-lg ${compact ? 'p-2' : 'p-3'} transition-all duration-200 ${
        hasVPattern
          ? 'border-red-200 bg-red-50 shadow-md'
          : 'border-gray-200 hover:border-gray-300'
      } ${onClick ? 'cursor-pointer hover:shadow-sm' : ''}`}
      onClick={handleClick}
    >
      <div className="flex items-center gap-3">
        {/* 股票信息 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className={`font-medium text-gray-900 ${compact ? 'text-sm' : 'text-base'}`}>
              {stock.name}
            </span>
            <span className={`text-gray-500 ${compact ? 'text-xs' : 'text-sm'}`}>
              {stock.code}
            </span>
            {hasVPattern && (
              <div className="flex items-center gap-1">
                <Activity className="w-3 h-3 text-red-500" />
                <span className="text-xs text-red-600 font-medium">V型</span>
              </div>
            )}
          </div>
          
          {/* 资金流入信息 */}
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-1">
              <span className={`text-xs text-gray-500 ${compact ? 'hidden' : ''}`}>
                主力净流入
              </span>
              <span 
                className={`font-bold ${compact ? 'text-sm' : 'text-base'}`}
                style={{ color: getFlowColor(latestFlow) }}
              >
                {formatMoneyAuto(latestFlow)}
              </span>
            </div>
            
            {/* 24小时变化 */}
            {change24h !== 0 && (
              <div className="flex items-center gap-1">
                {change24h > 0 ? (
                  <TrendingUp className="w-3 h-3 text-red-500" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-green-500" />
                )}
                <span 
                  className={`text-xs font-medium ${
                    change24h > 0 ? 'text-red-600' : 'text-green-600'
                  }`}
                >
                  {change24h > 0 ? '+' : ''}{formatMoneyAuto(change24h)}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* 迷你图表和刷新按钮 */}
        <div className="flex items-center gap-2 flex-shrink-0">
          {/* 迷你图表 */}
          <div className={`${compact ? 'w-16 h-8' : 'w-20 h-10'}`}>
            <MiniFlowChart
              klines={data.klines}
              height={compact ? 32 : 40}
              showVPattern={true}
            />
          </div>

          {/* 刷新按钮 */}
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className={`p-1 rounded-full transition-colors ${
              isRefreshing
                ? 'text-blue-400 cursor-not-allowed'
                : 'text-gray-400 hover:text-blue-500 hover:bg-blue-50'
            }`}
            title="刷新此股票数据"
          >
            <RefreshCw className={`w-3 h-3 ${isRefreshing ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default StockMonitorItem;
